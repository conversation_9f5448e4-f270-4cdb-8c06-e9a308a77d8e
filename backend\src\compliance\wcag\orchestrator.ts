import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import puppeteer from 'puppeteer';
import { v4 as uuidv4 } from 'uuid';
import { WcagDatabase } from './database/wcag-database';
import { WCAG_RULES, CATEGORY_WEIGHTS, VERSION_WEIGHTS, LEVEL_REQUIREMENTS } from './constants';
import { getCheckImplementation, getAutomationLevel } from './checks';
import { WcagRuleResult, WcagScanResult, WcagScanMetadata, WcagCheckResult, WcagManualReviewItem } from './types';
import logger from '../../utils/logger';

// Types
export interface WcagScanConfig {
  targetUrl: string;
  timeout?: number;
  wcagVersion?: '2.0' | '2.1' | '2.2' | '3.0';
  level?: 'A' | 'AA' | 'AAA';
  categories?: string[];
  rules?: string[];
  enableManualReview?: boolean;
  maxConcurrentChecks?: number;
  userId?: string;
  scanId?: string; // Optional scan ID to use instead of generating one
}

export interface ScanProgress {
  scanId: string;
  status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
  currentRule?: string;
  completedRules: string[];
  totalRules: number;
  progress: number;
  estimatedTimeRemaining?: number;
  startTime: Date;
  lastUpdate: Date;
}

// Local interfaces for orchestrator-specific data
export interface OrchestratorScanResult {
  scanId: string;
  targetUrl: string;
  config: WcagScanConfig;
  summary: OrchestratorScanSummary;
  ruleResults: WcagRuleResult[];
  scanProgress: ScanProgress;
  createdAt: Date;
  completedAt?: Date;
}

export interface OrchestratorScanSummary {
  overallScore: number;
  levelAchieved: 'A' | 'AA' | 'AAA' | 'None';
  riskLevel: 'Low' | 'Medium' | 'High' | 'Critical';
  totalRules: number;
  passedRules: number;
  failedRules: number;
  warningRules: number;
  categoryScores: Record<string, number>;
  versionScores: Record<string, number>;
  recommendations: string[];
}

// WcagRuleResult is now imported from types.ts

/**
 * WCAG Orchestrator - Part 7 Implementation
 * Coordinates comprehensive WCAG compliance scanning with all 21 rules
 */
export class WcagOrchestrator {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private database: WcagDatabase;
  private activeScanIds: Set<string> = new Set();
  private scanProgress: Map<string, ScanProgress> = new Map();

  constructor() {
    this.database = new WcagDatabase();
  }

  /**
   * Perform comprehensive WCAG scan
   */
  async performComprehensiveScan(config: WcagScanConfig): Promise<string> {
    const scanId = config.scanId || this.generateScanId();

    try {
      // Create scan record in database first
      const dbScanConfig: import('./types').WcagScanConfig = {
        targetUrl: config.targetUrl,
        userId: config.userId || 'unknown',
        requestId: scanId, // Use scanId as requestId for database compatibility
        scanOptions: {
          enableContrastAnalysis: true,
          enableKeyboardTesting: true,
          enableFocusAnalysis: true,
          enableSemanticValidation: true,
          wcagVersion: '2.2',
          level: config.level || 'AA',
          maxPages: 5,
          timeout: config.timeout || 30000,
        }
      };

      await WcagDatabase.createScan(dbScanConfig);
      logger.info(`✅ WCAG scan record created with ID: ${scanId}`);

      // Initialize scan progress
      await this.initializeScanProgress(scanId, config);

      // Launch browser and navigate to target
      await this.launchBrowser();
      
      // Get timeout from config or use default
      const navigationTimeout = config.timeout || 60000;
      await this.navigateToTarget(config.targetUrl, navigationTimeout);

      // Execute all checks
      const ruleResults = await this.executeAllChecks(scanId, config);

      // Count manual review items first
      const manualReviewItemsCount = ruleResults.reduce((count, result) => {
        return count + (result.manualReviewItems?.length || 0);
      }, 0);

      // Calculate scan summary
      const summary = await this.calculateScanSummary(ruleResults, config);

      // Create orchestrator scan result
      const orchestratorScanResult: OrchestratorScanResult = {
        scanId,
        targetUrl: config.targetUrl,
        config,
        summary,
        ruleResults,
        scanProgress: this.scanProgress.get(scanId)!,
        createdAt: new Date(),
        completedAt: new Date(),
      };

      // Convert to database-compatible format
      const dbScanResult: WcagScanResult = {
        scanId: orchestratorScanResult.scanId,
        targetUrl: orchestratorScanResult.targetUrl,
        status: 'completed',
        overallScore: summary.overallScore,
        levelAchieved:
          summary.levelAchieved === 'None' ? 'FAIL' : (summary.levelAchieved as 'A' | 'AA' | 'AAA'),
        riskLevel: summary.riskLevel.toLowerCase() as 'low' | 'medium' | 'high' | 'critical',
        summary: {
          totalAutomatedChecks: summary.totalRules,
          passedAutomatedChecks: summary.passedRules,
          failedAutomatedChecks: summary.failedRules,
          automatedScore: summary.overallScore,
          categoryScores: {
            perceivable: summary.categoryScores['perceivable'] || 0,
            operable: summary.categoryScores['operable'] || 0,
            understandable: summary.categoryScores['understandable'] || 0,
            robust: summary.categoryScores['robust'] || 0,
          },
          versionScores: {
            wcag21: summary.versionScores['2.1'] || 0,
            wcag22: summary.versionScores['2.2'] || 0,
            wcag30: summary.versionScores['3.0'] || 0,
          },
          automationRate: 0.87,
          manualReviewItems: manualReviewItemsCount,
          overallScore: summary.overallScore,
          totalRules: summary.totalRules,
          passedRules: summary.passedRules,
          failedRules: summary.failedRules,
          riskLevel: summary.riskLevel.toLowerCase() as 'low' | 'medium' | 'high' | 'critical',
          scanDuration: orchestratorScanResult.completedAt
            ? orchestratorScanResult.completedAt.getTime() - orchestratorScanResult.createdAt.getTime()
            : 0,
        },
        checks: ruleResults.map((rule: WcagRuleResult) => ({
          ruleId: rule.ruleId,
          ruleName: rule.ruleName,
          category: rule.category as 'perceivable' | 'operable' | 'understandable' | 'robust',
          wcagVersion: rule.wcagVersion as '2.1' | '2.2' | '3.0',
          successCriterion: rule.successCriterion || '',
          level: rule.level as 'A' | 'AA' | 'AAA',
          status: rule.status as 'passed' | 'failed' | 'not_applicable',
          score: rule.score,
          maxScore: rule.maxScore,
          weight: rule.weight || 1.0,
          automated: rule.automated,
          evidence: rule.evidence || [],
          recommendations: rule.recommendations || [],
          executionTime: rule.executionTime,
          errorMessage: rule.errorMessage,
          manualReviewItems: rule.manualReviewItems || [],
        })),
        recommendations: [],
        metadata: {
          scanId: orchestratorScanResult.scanId,
          userId: config.userId || '',
          requestId: orchestratorScanResult.scanId,
          startTime: orchestratorScanResult.createdAt,
          endTime: orchestratorScanResult.completedAt,
          duration: orchestratorScanResult.completedAt
            ? orchestratorScanResult.completedAt.getTime() - orchestratorScanResult.createdAt.getTime()
            : 0,
          userAgent: 'WCAG Scanner',
          viewport: { width: 1920, height: 1080 },
          environment: 'production',
          version: '1.0.0',
        },
        ruleResults: ruleResults,
      };

      // Store results in database with detailed logging
      try {
        logger.info(`💾 Saving WCAG scan result: ${scanId}`);
        logger.info(`📊 Scan summary: ${JSON.stringify({
          scanId,
          totalRules: dbScanResult.ruleResults?.length || 0,
          overallScore: summary.overallScore,
          levelAchieved: summary.levelAchieved,
          riskLevel: summary.riskLevel
        })}`);

        await this.database.saveScanResult(dbScanResult);
        logger.info(`✅ WCAG scan result saved successfully: ${scanId}`);

        // Store manual review items separately
        try {
          const allManualReviewItems = ruleResults.flatMap((result: WcagRuleResult) =>
            (result.manualReviewItems || []).map((item: WcagManualReviewItem) => ({
              ruleId: result.ruleId,
              selector: item.selector,
              description: item.description,
              automatedFindings: item.automatedFindings,
              reviewRequired: item.reviewRequired,
              priority: item.priority,
              estimatedTime: item.estimatedTime,
            })),
          );

          if (allManualReviewItems.length > 0) {
            await this.database.storeManualReviewItems(scanId, allManualReviewItems);
            logger.info(
              `✅ Stored ${allManualReviewItems.length} manual review items for scan ${scanId}`,
            );
          }
        } catch (manualReviewError: unknown) {
          logger.error(`⚠️ Failed to store manual review items for scan ${scanId}:`, {
            error: manualReviewError instanceof Error ? manualReviewError.message : 'Unknown error'
          });
          // Don't fail the entire scan for manual review storage issues
        }
      } catch (saveError) {
        logger.error(`❌ Error saving WCAG scan result`, {
          error: {
            message: saveError instanceof Error ? saveError.message : 'Unknown save error',
            stack: saveError instanceof Error ? saveError.stack : undefined,
            name: saveError instanceof Error ? saveError.name : 'UnknownError'
          },
          scanId,
          resultStructure: {
            hasRuleResults: !!dbScanResult.ruleResults,
            ruleResultsCount: dbScanResult.ruleResults?.length || 0,
            hasSummary: !!dbScanResult.summary,
            hasMetadata: !!dbScanResult.metadata
          }
        });
        throw new Error(`Failed to save WCAG scan result: ${saveError instanceof Error ? saveError.message : 'Unknown error'}`);
      }

      // Update progress to completed
      await this.updateScanProgress(scanId, 'completed', undefined, 100);

      logger.info(`✅ Comprehensive WCAG scan completed: ${scanId}`);
      logger.info(
        `📊 Overall Score: ${summary.overallScore}% | Level: ${summary.levelAchieved} | Risk: ${summary.riskLevel}`,
      );

      return scanId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.error(`❌ Error in comprehensive scan ${scanId}`, {
        error: {
          message: errorMessage,
          stack: errorStack,
          name: error instanceof Error ? error.name : 'UnknownError'
        },
        scanId,
        phase: 'comprehensive_scan'
      });

      await this.updateScanProgress(scanId, 'failed');
      throw error;
    } finally {
      await this.cleanupBrowser();
      this.activeScanIds.delete(scanId);
    }
  }

  /**
   * Get scan progress
   */
  async getScanProgress(scanId: string): Promise<ScanProgress | null> {
    // First check in-memory cache
    const memoryProgress = this.scanProgress.get(scanId);
    if (memoryProgress) {
      return memoryProgress;
    }

    // Then check database
    return await this.database.getScanProgress(scanId);
  }

  /**
   * Cancel active scan
   */
  async cancelScan(scanId: string): Promise<boolean> {
    if (!this.activeScanIds.has(scanId)) {
      return false;
    }

    await this.updateScanProgress(scanId, 'cancelled');
    this.activeScanIds.delete(scanId);

    logger.info(`🛑 Scan cancelled: ${scanId}`);
    return true;
  }

  /**
   * Initialize scan progress tracking
   */
  private async initializeScanProgress(scanId: string, config: WcagScanConfig): Promise<void> {
    const rulesToCheck = this.getRulesToCheck(config);

    const progress: ScanProgress = {
      scanId,
      status: 'initializing',
      completedRules: [],
      totalRules: rulesToCheck.length,
      progress: 0,
      startTime: new Date(),
      lastUpdate: new Date(),
    };

    this.scanProgress.set(scanId, progress);
    this.activeScanIds.add(scanId);

    await this.database.storeScanProgress(progress);

    logger.info(`🚀 Initialized scan ${scanId} with ${rulesToCheck.length} rules`);
  }

  /**
   * Update scan progress
   */
  private async updateScanProgress(
    scanId: string,
    status?: ScanProgress['status'],
    currentRule?: string,
    progressOverride?: number,
  ): Promise<void> {
    const progress = this.scanProgress.get(scanId);
    if (!progress) return;

    if (status) progress.status = status;
    if (currentRule) progress.currentRule = currentRule;

    // Calculate progress percentage
    if (progressOverride !== undefined) {
      progress.progress = progressOverride;
    } else {
      progress.progress = Math.round((progress.completedRules.length / progress.totalRules) * 100);
    }

    // Estimate time remaining
    if (progress.completedRules.length > 0 && progress.status === 'running') {
      const elapsed = Date.now() - progress.startTime.getTime();
      const avgTimePerRule = elapsed / progress.completedRules.length;
      const remainingRules = progress.totalRules - progress.completedRules.length;
      progress.estimatedTimeRemaining = Math.round((remainingRules * avgTimePerRule) / 1000);
    }

    progress.lastUpdate = new Date();

    await this.database.updateScanProgress(progress);

    logger.info(
      `📈 Scan ${scanId}: ${progress.progress}% (${progress.completedRules.length}/${progress.totalRules})`,
    );
  }

  /**
   * Launch browser instance
   */
  private async launchBrowser(): Promise<void> {
    if (this.browser) {
      await this.cleanupBrowser();
    }

    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
      ],
    });

    this.page = await this.browser.newPage();

    // Set viewport and user agent
    await this.page.setViewport({ width: 1920, height: 1080 });
    await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    logger.info('🌐 Browser launched successfully');
  }

  /**
   * Navigate to target URL
   */
  private async navigateToTarget(targetUrl: string, timeout: number = 60000): Promise<void> {
    if (!this.page) {
      throw new Error('Browser page not available');
    }

    logger.info(`🔗 Navigating to: ${targetUrl} (timeout: ${timeout}ms)`);

    await this.page.goto(targetUrl, {
      waitUntil: 'networkidle2',
      timeout, // Use the provided timeout value
    });

    // Wait for page to be fully loaded
    await new Promise((resolve) => setTimeout(resolve, 2000));

    logger.info('✅ Navigation completed');
  }

  /**
   * Execute all WCAG checks
   */
  private async executeAllChecks(
    scanId: string,
    config: WcagScanConfig,
  ): Promise<WcagRuleResult[]> {
    const rulesToCheck = this.getRulesToCheck(config);
    const results: WcagRuleResult[] = [];

    await this.updateScanProgress(scanId, 'running');

    logger.info(`🔍 Executing ${rulesToCheck.length} WCAG checks...`);

    for (const rule of rulesToCheck) {
      const startTime = Date.now();
      let checkImplementation: any = null;
      try {
        await this.updateScanProgress(scanId, 'running', rule.ruleId);
        checkImplementation = getCheckImplementation(rule.ruleId);

        if (!checkImplementation) {
          logger.warn(`⚠️ No implementation found for rule: ${rule.ruleId}`);
          continue;
        }

        // Create check configuration
        const checkConfig = {
          targetUrl: config.targetUrl,
          timeout: config.timeout || 30000,
          scanId,
          page: this.page!,
          enableManualReview: config.enableManualReview || false,
          enableManualTracking: config.enableManualReview || false,
          maxManualItems: 10,
        };

        // Execute the check
        logger.info(`🔍 Executing rule: ${rule.ruleName} (${rule.ruleId})`);
        const checkInstance = new checkImplementation();
        const checkResult = await checkInstance.performCheck(checkConfig);

        const executionTime = Date.now() - startTime;

        // Create rule result - use the checkResult directly as it matches WcagCheckResult interface
        const ruleResult: WcagRuleResult = {
          ...checkResult,
          ruleId: rule.ruleId,
          ruleName: rule.ruleName,
          category: rule.category as 'perceivable' | 'operable' | 'understandable' | 'robust',
          wcagVersion: rule.wcagVersion as '2.1' | '2.2' | '3.0',
          successCriterion: (rule as any).successCriterion || checkResult.successCriterion || '',
          level: rule.level as 'A' | 'AA' | 'AAA',
          weight: rule.weight || checkResult.weight || 1.0,
          executionTime,
        };

        results.push(ruleResult);

        // Update progress
        const progress = this.scanProgress.get(scanId);
        if (progress) {
          progress.completedRules.push(rule.ruleId);
          await this.updateScanProgress(scanId);
        }

        logger.info(
          `✅ Rule ${rule.ruleId} completed: ${checkResult.status} (${checkResult.score}/${ruleResult.maxScore})`,
        );
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const errorStack = error instanceof Error ? error.stack : undefined;
        const executionTime = Date.now() - startTime;

        logger.error(`❌ Error executing rule ${rule.ruleId}`, {
          error: {
            message: errorMessage,
            stack: errorStack,
            name: error instanceof Error ? error.name : 'UnknownError'
          },
          ruleId: rule.ruleId,
          ruleName: rule.ruleName,
          executionTime,
          checkImplementation: checkImplementation?.name || 'Unknown'
        });

        // Create failed result
        const failedResult: WcagRuleResult = {
          ruleId: rule.ruleId,
          ruleName: rule.ruleName,
          category: rule.category as 'perceivable' | 'operable' | 'understandable' | 'robust',
          wcagVersion: rule.wcagVersion as '2.1' | '2.2' | '3.0',
          successCriterion: (rule as any).successCriterion || '',
          level: rule.level as 'A' | 'AA' | 'AAA',
          status: 'failed',
          score: 0,
          maxScore: 100,
          weight: rule.weight || 1.0,
          automated: true,
          evidence: [{
            type: 'text',
            description: 'Rule execution failed',
            value: errorMessage,
            severity: 'error',
          }],
          recommendations: [
            'Manual review required due to execution error',
            `Error details: ${errorMessage}`,
            'Check server logs for more information'
          ],
          executionTime,
          errorMessage,
        };

        results.push(failedResult);
      }
    }

    logger.info(`✅ All checks completed: ${results.length} results`);
    return results;
  }

  /**
   * Calculate comprehensive scan summary
   */
  private async calculateScanSummary(
    results: WcagRuleResult[],
    config: WcagScanConfig,
  ): Promise<OrchestratorScanSummary> {
    const totalRules = results.length;
    const passedRules = results.filter((r) => r.status === 'passed').length;
    const failedRules = results.filter((r) => r.status === 'failed').length;
    const warningRules = results.filter((r) => r.status === 'not_applicable').length;

    // Calculate overall score
    const overallScore = this.calculateOverallScore(results);

    // Determine WCAG level achieved
    const levelAchieved = this.determineLevelAchieved(results, config);

    // Determine risk level
    const riskLevel = this.determineRiskLevel(overallScore, failedRules, totalRules);

    // Calculate category scores
    const categoryScores = this.calculateCategoryScores(results);

    // Calculate version scores
    const versionScores = this.calculateVersionScores(results);

    // Generate recommendations
    const recommendations = this.generateRecommendations(results, overallScore, levelAchieved);

    return {
      overallScore,
      levelAchieved,
      riskLevel,
      totalRules,
      passedRules,
      failedRules,
      warningRules,
      categoryScores,
      versionScores,
      recommendations,
    };
  }

  /**
   * Calculate overall weighted score
   * FIXED: Corrected scoring logic to properly handle pass/fail scenarios
   */
  private calculateOverallScore(results: WcagRuleResult[]): number {
    if (results.length === 0) return 0;

    let totalWeightedScore = 0;
    let totalWeight = 0;

    // Debug logging to track scoring calculation
    console.log('🔍 WCAG Scoring Debug - Input Results:', results.map(r => ({
      ruleId: r.ruleId,
      status: r.status,
      score: r.score,
      maxScore: r.maxScore,
      category: r.category,
      wcagVersion: r.wcagVersion,
      weight: r.weight
    })));

    for (const result of results) {
      // Skip not_applicable results from scoring calculation
      if (result.status === 'not_applicable') {
        console.log(`⏭️ Skipping not_applicable rule: ${result.ruleId}`);
        continue;
      }

      const categoryWeight =
        CATEGORY_WEIGHTS[result.category as keyof typeof CATEGORY_WEIGHTS] || 1;
      const versionWeight = VERSION_WEIGHTS[result.wcagVersion as keyof typeof VERSION_WEIGHTS] || 1;
      const combinedWeight = categoryWeight * versionWeight * (result.weight || 1);

      // Calculate normalized score (0-100)
      const normalizedScore = result.maxScore > 0 ? (result.score / result.maxScore) * 100 : 0;

      console.log(`📊 Rule ${result.ruleId}: score=${result.score}/${result.maxScore} (${normalizedScore.toFixed(1)}%), weight=${combinedWeight.toFixed(3)}`);

      totalWeightedScore += normalizedScore * combinedWeight;
      totalWeight += combinedWeight;
    }

    const finalScore = totalWeight > 0 ? Math.round(totalWeightedScore / totalWeight) : 0;

    console.log(`🎯 WCAG Final Score Calculation:
      - Total Weighted Score: ${totalWeightedScore.toFixed(2)}
      - Total Weight: ${totalWeight.toFixed(3)}
      - Final Score: ${finalScore}%
      - Rules Processed: ${results.filter(r => r.status !== 'not_applicable').length}/${results.length}
      - Passed Rules: ${results.filter(r => r.status === 'passed').length}
      - Failed Rules: ${results.filter(r => r.status === 'failed').length}`);

    return finalScore;
  }

  /**
   * Determine WCAG level achieved
   */
  private determineLevelAchieved(
    results: WcagRuleResult[],
    config: WcagScanConfig,
  ): 'A' | 'AA' | 'AAA' | 'None' {
    const targetLevel = config.level || 'AA';

    // Check each level in order
    for (const level of ['A', 'AA', 'AAA'] as const) {
      const levelThreshold = LEVEL_REQUIREMENTS[level];
      const levelResults = results.filter((r) => {
        if (level === 'A') return r.level === 'A';
        if (level === 'AA') return r.level === 'A' || r.level === 'AA';
        return true; // AAA includes all levels
      });

      if (levelResults.length === 0) continue;

      const passedCount = levelResults.filter((r) => r.status === 'passed').length;
      const passRate = passedCount / levelResults.length;

      // Use the threshold from LEVEL_REQUIREMENTS
      if (passRate < levelThreshold) {
        // Return previous level or None
        if (level === 'A') return 'None';
        if (level === 'AA') return 'A';
        if (level === 'AAA') return 'AA';
      }

      // If we've reached the target level, return it
      if (level === targetLevel) return level;
    }

    return targetLevel;
  }

  /**
   * Determine risk level based on score and failures
   */
  private determineRiskLevel(
    overallScore: number,
    failedRules: number,
    totalRules: number,
  ): 'Low' | 'Medium' | 'High' | 'Critical' {
    const failureRate = totalRules > 0 ? failedRules / totalRules : 0;

    if (overallScore >= 90 && failureRate <= 0.05) return 'Low';
    if (overallScore >= 75 && failureRate <= 0.15) return 'Medium';
    if (overallScore >= 50 && failureRate <= 0.3) return 'High';
    return 'Critical';
  }

  /**
   * Calculate category-specific scores
   */
  private calculateCategoryScores(results: WcagRuleResult[]): Record<string, number> {
    const categoryScores: Record<string, number> = {};
    const categoryGroups: Record<string, WcagRuleResult[]> = {};

    // Group results by category
    for (const result of results) {
      if (!categoryGroups[result.category]) {
        categoryGroups[result.category] = [];
      }
      categoryGroups[result.category].push(result);
    }

    // Calculate score for each category
    for (const [category, categoryResults] of Object.entries(categoryGroups)) {
      const totalScore = categoryResults.reduce((sum, r) => sum + r.score, 0);
      const maxScore = categoryResults.reduce((sum, r) => sum + r.maxScore, 0);
      categoryScores[category] = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;
    }

    return categoryScores;
  }

  /**
   * Calculate version-specific scores
   */
  private calculateVersionScores(results: WcagRuleResult[]): Record<string, number> {
    const versionScores: Record<string, number> = {};
    const versionGroups: Record<string, WcagRuleResult[]> = {};

    // Group results by version
    for (const result of results) {
      if (!versionGroups[result.wcagVersion]) {
        versionGroups[result.wcagVersion] = [];
      }
      versionGroups[result.wcagVersion].push(result);
    }

    // Calculate score for each version
    for (const [version, versionResults] of Object.entries(versionGroups)) {
      const totalScore = versionResults.reduce((sum, r) => sum + r.score, 0);
      const maxScore = versionResults.reduce((sum, r) => sum + r.maxScore, 0);
      versionScores[version] = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;
    }

    return versionScores;
  }

  /**
   * Generate improvement recommendations
   */
  private generateRecommendations(
    results: WcagRuleResult[],
    overallScore: number,
    levelAchieved: string,
  ): string[] {
    const recommendations: string[] = [];

    // Overall score recommendations
    if (overallScore < 50) {
      recommendations.push(
        'Critical accessibility issues detected. Immediate remediation required.',
      );
    } else if (overallScore < 75) {
      recommendations.push(
        'Significant accessibility improvements needed to meet compliance standards.',
      );
    } else if (overallScore < 90) {
      recommendations.push('Good accessibility foundation with room for improvement.');
    }

    // Level-specific recommendations
    if (levelAchieved === 'None') {
      recommendations.push('Focus on basic Level A compliance requirements first.');
    } else if (levelAchieved === 'A') {
      recommendations.push('Work towards Level AA compliance for better accessibility.');
    } else if (levelAchieved === 'AA') {
      recommendations.push('Consider Level AAA enhancements for optimal accessibility.');
    }

    // Category-specific recommendations
    const failedResults = results.filter((r) => r.status === 'failed');
    const categoryFailures: Record<string, number> = {};

    for (const result of failedResults) {
      categoryFailures[result.category] = (categoryFailures[result.category] || 0) + 1;
    }

    // Add category-specific recommendations
    for (const [category, count] of Object.entries(categoryFailures)) {
      if (count >= 3) {
        recommendations.push(
          `Address multiple issues in ${category} category (${count} failures).`,
        );
      }
    }

    // Rule-specific recommendations
    for (const result of failedResults.slice(0, 5)) {
      // Top 5 failures
      if (result.recommendations.length > 0) {
        recommendations.push(`${result.ruleName}: ${result.recommendations[0]}`);
      }
    }

    return recommendations.slice(0, 10); // Limit to 10 recommendations
  }

  /**
   * Get rules to check based on configuration
   */
  private getRulesToCheck(config: WcagScanConfig): Array<{
    ruleId: string;
    ruleName: string;
    category: string;
    wcagVersion: string;
    level: string;
    weight: number;
    automated: boolean;
    description: string;
    checkFunction: string;
  }> {
    let rules = WCAG_RULES;

    // Filter by version
    if (config.wcagVersion) {
      rules = rules.filter((rule) => rule.wcagVersion === config.wcagVersion);
    }

    // Filter by level
    if (config.level) {
      const allowedLevels =
        config.level === 'A' ? ['A'] : config.level === 'AA' ? ['A', 'AA'] : ['A', 'AA', 'AAA'];
      rules = rules.filter((rule) => allowedLevels.includes(rule.level));
    }

    // Filter by categories
    if (config.categories && config.categories.length > 0) {
      rules = rules.filter((rule) => config.categories!.includes(rule.category));
    }

    // Filter by specific rules
    if (config.rules && config.rules.length > 0) {
      rules = rules.filter((rule) => config.rules!.includes(rule.ruleId));
    }

    return rules;
  }

  /**
   * Create empty scan summary
   */
  private _createEmptySummary(): OrchestratorScanSummary {
    return {
      overallScore: 0,
      levelAchieved: 'None',
      riskLevel: 'Critical',
      totalRules: 0,
      passedRules: 0,
      failedRules: 0,
      warningRules: 0,
      categoryScores: {
        perceivable: 0,
        operable: 0,
        understandable: 0,
        robust: 0,
      },
      versionScores: {
        '2.1': 0,
        '2.2': 0,
        '3.0': 0,
      },
      recommendations: ['No scan data available'],
    };
  }

  /**
   * Generate unique scan ID
   */
  private generateScanId(): string {
    return `wcag_${Date.now()}_${uuidv4().substring(0, 8)}`;
  }

  /**
   * Cleanup browser resources
   */
  private async cleanupBrowser(): Promise<void> {
    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }

      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
    } catch (error) {
      logger.error('Error during browser cleanup', { error });
    }
  }

  /**
   * Get total checks count for configuration
   */
  private _getTotalChecksCount(config: WcagScanConfig): number {
    return this.getRulesToCheck(config).length;
  }
}
