'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { WcagScanResult, WcagCheck, WcagCheckEvidence } from '@/types/wcag';
import wcagApiService from '@/services/wcag-api';
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  FileText,
  Eye,
  Focus,
  MousePointer,
  Keyboard,
} from 'lucide-react';

interface WcagManualReviewItem {
  ruleId: string;
  ruleName: string;
  category: string;
  selector: string;
  description: string;
  automatedFindings: string;
  reviewRequired: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number;
  reviewStatus: 'pending' | 'in_progress' | 'completed';
  reviewNotes?: string;
  reviewerAssessment?: 'compliant' | 'non_compliant' | 'needs_improvement';
  reviewDate?: string;
  reviewerId?: string;
}

interface WcagManualReviewSummary {
  totalItems: number;
  pendingReview: number;
  inProgress: number;
  completed: number;
  complianceRate: number;
}

interface WcagManualReviewDashboardProps {
  scanResult: WcagScanResult;
  onReviewUpdate?: (scanId: string, updatedScore: number) => void;
}

export function WcagManualReviewDashboard({ 
  scanResult, 
  onReviewUpdate 
}: WcagManualReviewDashboardProps) {
  const [reviewNotes, setReviewNotes] = useState<Record<string, string>>({});
  const [reviewAssessments, setReviewAssessments] = useState<Record<string, string>>({});
  const [reviewStatuses, setReviewStatuses] = useState<Record<string, 'pending' | 'completed'>>({});
  const [savingReviews, setSavingReviews] = useState<Record<string, boolean>>({});

  // Extract manual review items from scan result
  const getManualReviewItems = (): WcagManualReviewItem[] => {
    const items: WcagManualReviewItem[] = [];

    // Get manual review items from checks that have them
    scanResult.checks?.forEach((check) => {
      // Check if this is a semi-automated or manual check that needs review
      if (check.automationLevel === 'semi-automated' || check.automationLevel === 'manual') {
        // For semi-automated checks that failed or have warnings
        if (check.result === 'fail' || check.result === 'cantTell') {
          check.evidence?.forEach((evidence, index) => {
            items.push({
              ruleId: check.ruleId,
              ruleName: check.title,
              category: check.category,
              selector: evidence.selector || `element-${index}`,
              description: evidence.description,
              automatedFindings: `${check.description} - ${evidence.description}`,
              reviewRequired: `Manual review required for ${check.title} (${check.level} level)`,
              priority:
                check.impact === 'critical' || check.impact === 'serious'
                  ? 'high'
                  : check.impact === 'moderate'
                    ? 'medium'
                    : 'low',
              estimatedTime: 5, // Default 5 minutes
              reviewStatus: 'pending',
            });
          });
        }
      }
    });

    // If no items found from checks, create some based on summary
    if (items.length === 0 && scanResult.summary?.manualReviewItems > 0) {
      // Create placeholder items based on the summary count
      for (let i = 0; i < scanResult.summary.manualReviewItems; i++) {
        items.push({
          ruleId: `MANUAL-${i + 1}`,
          ruleName: `Manual Review Item ${i + 1}`,
          category: 'operable',
          selector: `manual-review-${i + 1}`,
          description: 'This item requires manual accessibility review',
          automatedFindings:
            'Automated checks identified potential accessibility issues that require human verification',
          reviewRequired: 'Manual accessibility expert review required',
          priority: 'medium',
          estimatedTime: 10,
          reviewStatus: 'pending',
        });
      }
    }

    return items;
  };

  const manualReviewItems = getManualReviewItems();

  // Calculate summary statistics
  const getManualReviewSummary = (): WcagManualReviewSummary => {
    const totalItems = manualReviewItems.length;
    let completed = 0;
    let pendingReview = 0;
    let inProgress = 0;

    manualReviewItems.forEach((item) => {
      const itemKey = `${item.ruleId}-${item.selector}`;
      const status = reviewStatuses[itemKey] || item.reviewStatus;

      if (status === 'completed') {
        completed++;
      } else if (status === 'in_progress') {
        inProgress++;
      } else {
        pendingReview++;
      }
    });

    const complianceRate = totalItems > 0 ? (completed / totalItems) * 100 : 0;

    return {
      totalItems,
      pendingReview,
      inProgress,
      completed,
      complianceRate,
    };
  };

  const summary = getManualReviewSummary();

  const handleReviewUpdate = (itemKey: string, field: 'notes' | 'assessment', value: string) => {
    if (field === 'notes') {
      setReviewNotes((prev) => ({ ...prev, [itemKey]: value }));
    } else {
      setReviewAssessments((prev) => ({ ...prev, [itemKey]: value }));
    }
  };

  const handleSaveReview = async (itemKey: string) => {
    const assessment = reviewAssessments[itemKey];
    const notes = reviewNotes[itemKey];

    if (!assessment) {
      console.warn('Cannot save review without assessment');
      return;
    }

    setSavingReviews((prev) => ({ ...prev, [itemKey]: true }));

    try {
      // Parse itemKey to get ruleId
      const [ruleId] = itemKey.split('-');

      console.log('💾 Saving WCAG manual review:', {
        scanId: scanResult.scanId,
        ruleId,
        assessment,
        notes,
        timestamp: new Date().toISOString(),
      });

      // Call WCAG API to save review
      const result = await wcagApiService.updateManualReview(
        scanResult.scanId,
        ruleId,
        assessment,
        notes,
        'Accessibility Reviewer',
      );

      if (result.success) {
        setReviewStatuses((prev) => ({ ...prev, [itemKey]: 'completed' }));
        console.log('✅ WCAG manual review saved successfully');
        
        if (onReviewUpdate && result.updatedScore) {
          onReviewUpdate(scanResult.scanId, result.updatedScore);
        }
      }
    } catch (error) {
      console.error('❌ Failed to save WCAG manual review:', error);
    } finally {
      setSavingReviews((prev) => ({ ...prev, [itemKey]: false }));
    }
  };

  const getManualReviewIcon = (ruleId: string) => {
    if (ruleId.includes('focus')) return <Focus className="h-5 w-5 text-blue-500" />;
    if (ruleId.includes('keyboard')) return <Keyboard className="h-5 w-5 text-green-500" />;
    if (ruleId.includes('target')) return <MousePointer className="h-5 w-5 text-purple-500" />;
    return <Eye className="h-5 w-5 text-orange-500" />;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  if (manualReviewItems.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Manual Review Items</CardTitle>
          <CardDescription>Items requiring human accessibility expertise assessment</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Manual Review Required
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              All accessibility checks were completed automatically. No manual review items were identified.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalItems}</div>
            <p className="text-xs text-muted-foreground">Requiring manual review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{summary.pendingReview}</div>
            <p className="text-xs text-muted-foreground">Awaiting assessment</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.completed}</div>
            <p className="text-xs text-muted-foreground">Reviews completed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(summary.complianceRate)}%</div>
            <p className="text-xs text-muted-foreground">Review progress</p>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Review Progress</CardTitle>
          <CardDescription>Overall progress of manual accessibility reviews</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Completion Rate</span>
              <span>{Math.round(summary.complianceRate)}%</span>
            </div>
            <Progress value={summary.complianceRate} className="w-full" />
            <p className="text-xs text-muted-foreground">
              {summary.completed} of {summary.totalItems} items reviewed
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Manual Review Items */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Review Items</CardTitle>
          <CardDescription>Items requiring human accessibility expertise assessment</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {manualReviewItems.map((item, index) => {
              const itemKey = `${item.ruleId}-${item.selector}`;
              const isCompleted = reviewStatuses[itemKey] === 'completed';
              const isSaving = savingReviews[itemKey];

              return (
                <div key={itemKey} className="border rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getManualReviewIcon(item.ruleId)}
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {item.ruleName}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {item.category} • {item.selector}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getPriorityColor(item.priority)}>
                        {item.priority} priority
                      </Badge>
                      <Badge variant="outline">
                        <Clock className="h-3 w-3 mr-1" />
                        {item.estimatedTime}m
                      </Badge>
                      {isCompleted && (
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Completed
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h5 className="font-medium text-sm text-gray-900 dark:text-white mb-2">
                        Description
                      </h5>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {item.description}
                      </p>
                    </div>

                    <div>
                      <h5 className="font-medium text-sm text-gray-900 dark:text-white mb-2">
                        Automated Findings
                      </h5>
                      <p className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded">
                        {item.automatedFindings}
                      </p>
                    </div>

                    <div>
                      <h5 className="font-medium text-sm text-gray-900 dark:text-white mb-2">
                        Review Required
                      </h5>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {item.reviewRequired}
                      </p>
                    </div>

                    {!isCompleted && (
                      <div className="space-y-4 pt-4 border-t">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor={`assessment-${itemKey}`} className="text-sm font-medium">
                              Assessment *
                            </Label>
                            <Select
                              value={reviewAssessments[itemKey] || ''}
                              onValueChange={(value) => handleReviewUpdate(itemKey, 'assessment', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select assessment" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="compliant">Compliant</SelectItem>
                                <SelectItem value="non_compliant">Non-Compliant</SelectItem>
                                <SelectItem value="needs_improvement">Needs Improvement</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div>
                          <Label htmlFor={`notes-${itemKey}`} className="text-sm font-medium">
                            Review Notes
                          </Label>
                          <Textarea
                            id={`notes-${itemKey}`}
                            placeholder="Add your review notes and recommendations..."
                            value={reviewNotes[itemKey] || ''}
                            onChange={(e) => handleReviewUpdate(itemKey, 'notes', e.target.value)}
                            className="mt-1"
                            rows={3}
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button
                            onClick={() => handleSaveReview(itemKey)}
                            disabled={!reviewAssessments[itemKey] || isSaving}
                            className="flex items-center gap-2"
                          >
                            {isSaving ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                Saving...
                              </>
                            ) : (
                              <>
                                <CheckCircle className="h-4 w-4" />
                                Save Review
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}

                    {isCompleted && (
                      <div className="pt-4 border-t">
                        <Alert>
                          <CheckCircle className="h-4 w-4" />
                          <AlertDescription>
                            This item has been reviewed and marked as{' '}
                            <strong>{reviewAssessments[itemKey]?.replace('_', ' ')}</strong>.
                            {reviewNotes[itemKey] && (
                              <div className="mt-2 text-sm">
                                <strong>Notes:</strong> {reviewNotes[itemKey]}
                              </div>
                            )}
                          </AlertDescription>
                        </Alert>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
